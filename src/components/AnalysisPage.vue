<script setup lang="ts">
import { ref, onMounted, shallowRef, computed } from 'vue'
import { language } from '../stores/language'

import NetworkGraph from './NetworkGraph.vue'
import ThinkingModelSelector from './ThinkingModelSelector.vue'
import { generateSystemicAnalysis, loadAnalysisResult, type ThinkingModel, type AnalysisResult } from '../utils/systemicThinking'
import { isLoading, loadingMessage, currentLoadingStep, totalLoadingSteps, startLoading, stopLoading, updateLoadingProgress } from '../utils/loadingState'

const topic = ref('')
const thinkingDepth = ref(2)
const selectedModels = ref<ThinkingModel[]>(['butterfly-effect', 'premortem', 'red-team'])
const analysisResult = shallowRef<AnalysisResult | null>(null)
const analysisError = ref('')
const isControlPanelCollapsed = ref(false)
const isSidebarCollapsed = ref(true) // 移动端默认折叠侧边栏

const translations = computed(() => {
  const modelDescriptions = {
    'butterfly-effect': language.value === 'zh' ? '蝴蝶效应分析' : 'Butterfly Effect Analysis',
    'premortem': language.value === 'zh' ? '事前验尸分析' : 'Pre-mortem Analysis',
    'red-team': language.value === 'zh' ? '红队模拟分析' : 'Red Team Simulation Analysis',
    'systems-thinking': language.value === 'zh' ? '系统思维分析' : 'Systems Thinking Analysis',
    'scenario-planning': language.value === 'zh' ? '情景规划分析' : 'Scenario Planning Analysis',
    'root-cause': language.value === 'zh' ? '根因分析' : 'Root Cause Analysis'
  };

  const depthLabels = {
    1: language.value === 'zh' ? '浅层分析' : 'Shallow Analysis',
    2: language.value === 'zh' ? '标准分析' : 'Standard Analysis',
    3: language.value === 'zh' ? '深度分析' : 'Deep Analysis',
    4: language.value === 'zh' ? '全面分析' : 'Comprehensive Analysis',
    5: language.value === 'zh' ? '极深分析' : 'Extensive Analysis'
  };

  return {
    pageTitle: language.value === 'zh' ? '系统化分析' : 'Systemic Analysis',
    topicPrefix: language.value === 'zh' ? '主题：' : 'Topic: ',
    exportAnalysis: language.value === 'zh' ? '导出分析结果' : 'Export Analysis Result',
    export: language.value === 'zh' ? '导出' : 'Export',
    analysisConfig: language.value === 'zh' ? '分析配置' : 'Analysis Configuration',
    thinkingDepth: language.value === 'zh' ? '思考深度：' : 'Thinking Depth:',
    depthShallow: depthLabels[1],
    depthStandard: depthLabels[2],
    depthDeep: depthLabels[3],
    depthComprehensive: depthLabels[4],
    depthExtensive: depthLabels[5],
    startAnalysis: language.value === 'zh' ? '开始AI系统化分析' : 'Start AI Systemic Analysis',
    reAnalyze: language.value === 'zh' ? '重新分析' : 'Re-Analyze',
    analyzing: language.value === 'zh' ? 'AI分析中...' : 'AI Analyzing...',
    analysisFailed: language.value === 'zh' ? '分析失败' : 'Analysis Failed',
    retry: language.value === 'zh' ? '重试' : 'Retry',
    configureAnalysis: language.value === 'zh' ? '配置分析参数' : 'Configure Analysis Parameters',
    configureAnalysisPrompt: language.value === 'zh' ? '请选择思维模型和分析深度，然后点击"开始AI系统化分析"按钮' : 'Please select thinking models and analysis depth, then click "Start AI Systemic Analysis" button',
    analysisTips: language.value === 'zh' ? '分析提示' : 'Analysis Tips',
    tip1: language.value === 'zh' ? '选择多个思维模型可获得更全面的分析' : 'Selecting multiple thinking models provides a more comprehensive analysis',
    tip2: language.value === 'zh' ? '思考深度越高，分析越详细但耗时更长' : 'Higher thinking depth leads to more detailed but time-consuming analysis',
    tip3: language.value === 'zh' ? '建议首次使用选择2-3个模型，标准深度' : 'For first use, it is recommended to select 2-3 models with standard depth',
    analysisDetails: language.value === 'zh' ? '分析详情' : 'Analysis Details',
    analysisNodes: language.value === 'zh' ? '分析节点' : 'Analysis Nodes',
    connections: language.value === 'zh' ? '关联连接' : 'Connections',
    totalTokens: language.value === 'zh' ? '总Token' : 'Total Tokens',
    tokensPerSecond: language.value === 'zh' ? 'Token/秒' : 'Tokens/Second',
    keyInsights: language.value === 'zh' ? '关键洞察' : 'Key Insights',
    nodeDetails: language.value === 'zh' ? '节点详情' : 'Node Details',
    nodeId: language.value === 'zh' ? 'ID:' : 'ID:',
    nodeType: language.value === 'zh' ? '类型:' : 'Type:',
    nodeDescription: language.value === 'zh' ? '描述:' : 'Description:',
    nodeProperties: language.value === 'zh' ? '属性:' : 'Properties:',
    detailedAnalysis: language.value === 'zh' ? '详细分析' : 'Detailed Analysis',
    keyFindings: language.value === 'zh' ? '关键发现:' : 'Key Findings:',
    recommendations: language.value === 'zh' ? '建议:' : 'Recommendations:',
    unknownCategory: language.value === 'zh' ? '未知类别' : 'Unknown Category',
    unknownDifficulty: language.value === 'zh' ? '未知难度' : 'Unknown Difficulty',
    totalRecords: language.value === 'zh' ? '条记录' : 'records',
    questions: language.value === 'zh' ? '个问题' : 'questions',
    clearAll: language.value === 'zh' ? '清空全部' : 'Clear All',
    delete: language.value === 'zh' ? '删除' : 'Delete',
    viewDetails: language.value === 'zh' ? '查看详情' : 'View Details',
    loadUse: language.value === 'zh' ? '加载使用' : 'Load & Use',
    noMatchingRecords: language.value === 'zh' ? '没有找到匹配的记录' : 'No matching records found',
    noHistoryYet: language.value === 'zh' ? '暂无历史记录' : 'No history yet',
    tryDifferentKeywords: language.value === 'zh' ? '尝试使用其他关键词搜索' : 'Try searching with different keywords',
    historyWillAppear: language.value === 'zh' ? '开始生成问题后，历史记录会显示在这里' : 'History will appear here after you generate questions',
    exporting: language.value === 'zh' ? '导出中...' : 'Exporting...',
    exportFailed: language.value === 'zh' ? '导出失败，请重试' : 'Export failed, please try again',
    confirmDelete: language.value === 'zh' ? '确定要删除这条历史记录吗？' : 'Are you sure you want to delete this history record?',
    confirmClearAll: language.value === 'zh' ? '确定要清空所有历史记录吗？此操作不可恢复。' : 'Are you sure you want to clear all history records? This action cannot be undone?',
    systemAnalysisReport: language.value === 'zh' ? '系统化分析报告' : 'Systemic Analysis Report',
    analysisTopic: language.value === 'zh' ? '分析主题' : 'Analysis Topic',
    analysisTime: language.value === 'zh' ? '分析时间' : 'Analysis Time',
    thinkingDepthLabel: language.value === 'zh' ? '思考深度' : 'Thinking Depth',
    usedModels: language.value === 'zh' ? '使用模型' : 'Used Models',
    analysisOverview: language.value === 'zh' ? '分析概况' : 'Analysis Overview',
    nodeTitle: language.value === 'zh' ? '节点标题' : 'Node Title',
    questionCategory: language.value === 'zh' ? '类别' : 'Category',
    questionDifficulty: language.value === 'zh' ? '难度' : 'Difficulty',
    estimatedTime: language.value === 'zh' ? '预估时间' : 'Estimated Time',
    questionDescription: language.value === 'zh' ? '描述' : 'Description',
    thinkingHints: language.value === 'zh' ? '思考提示' : 'Thinking Hints',
    currentLanguage: language.value,
    modelDescriptions: modelDescriptions,
    depthLabels: depthLabels
  };
});

// 性能优化：计算属性缓存
const hasValidConfig = computed(() => selectedModels.value.length > 0)
const canStartAnalysis = computed(() => topic.value.trim() && hasValidConfig.value && !isLoading.value)

onMounted(() => {
  // 从URL参数获取主题
  const urlParams = new URLSearchParams(window.location.search)
  const topicParam = urlParams.get('topic')
  if (topicParam) {
    topic.value = decodeURIComponent(topicParam)
    // 尝试加载之前保存的分析结果
    const savedAnalysis = loadAnalysisResult(topic.value)
    if (savedAnalysis) {
      analysisResult.value = savedAnalysis
    }
  }
})

const startAnalysis = async () => {
  if (!canStartAnalysis.value) return
  
  startLoading('AI分析中...', selectedModels.value.length)
  analysisError.value = ''
  analysisResult.value = null // 清除之前的结果
  
  try {
    analysisResult.value = await generateSystemicAnalysis(
      topic.value,
      selectedModels.value,
      thinkingDepth.value,
      (step: number) => {
        updateLoadingProgress(step)
      }
    )
  } catch (error) {
    console.error('分析失败:', error)
    analysisError.value = error instanceof Error ? error.message : '分析失败，请重试'
  } finally {
    stopLoading()
    isControlPanelCollapsed.value = true
  }
}



const exportAnalysis = () => {
  if (!analysisResult.value) return

  let htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>系统化分析报告 - ${topic.value}</title>
      <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
        h1, h2, h3, h4 { color: #2563eb; }
        h1 { border-bottom: 2px solid #2563eb; padding-bottom: 10px; margin-bottom: 20px; }
        h2 { margin-top: 30px; border-bottom: 1px solid #eee; padding-bottom: 5px; }
        h3 { margin-top: 20px; color: #1e40af; }
        .section { margin-bottom: 20px; padding: 15px; background: #f8fafc; border-radius: 8px; border: 1px solid #eee; }
        .section-dark { background: #e0f2fe; }
        ul { list-style-type: disc; margin-left: 20px; }
        ol { margin-left: 20px; }
        .node-card { background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.05); }
        .node-card h4 { margin-top: 0; margin-bottom: 5px; color: #059669; }
        .node-card p { margin-bottom: 5px; font-size: 0.9em; }
        .node-card strong { color: #333; }
        .model-result { background: #f0f9ff; border: 1px solid #bfdbfe; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
        .model-result h4 { color: #059669; }
        .model-result ul { list-style-type: circle; }
      </style>
    </head>
    <body>
      <h1>系统化分析报告</h1>
      <div class="section">
        <p><strong>分析主题:</strong> ${topic.value}</p>
        <p><strong>分析时间:</strong> ${new Date().toLocaleString('zh-CN')}</p>
        <p><strong>思考深度:</strong> ${translations.value.depthLabels[thinkingDepth.value as keyof typeof translations.value.depthLabels]}</p>
        <p><strong>使用模型:</strong> ${selectedModels.value.join(', ')}</p>
      </div>

      <h2>📊 分析概况</h2>
      <div class="section section-dark">
        <p><strong>分析节点:</strong> ${analysisResult.value.summary.totalNodes} 个</p>
        <p><strong>关联连接:</strong> ${analysisResult.value.summary.totalConnections} 个</p>
      </div>
  `

  if (analysisResult.value.summary.keyInsights.length > 0) {
    htmlContent += `
      <h2>💡 关键洞察</h2>
      <div class="section">
        <ul>
          ${analysisResult.value.summary.keyInsights.map(insight => `<li>${insight}</li>`).join('')}
        </ul>
      </div>
    `
  }

  htmlContent += `
    <h2>节点详情</h2>
    <div class="section">
      ${analysisResult.value.nodes.map(node => `
        <div class="node-card">
          <h4>${node.title}</h4>
          <p><strong>ID:</strong> ${node.id}</p>
          <p><strong>类型:</strong> ${translations.value.modelDescriptions[node.modelSource as keyof typeof translations.value.modelDescriptions] || node.modelSource}</p>
          ${node.description ? `<p><strong>描述:</strong> ${node.description}</p>` : ''}
          ${node.properties ? `<p><strong>属性:</strong> ${JSON.stringify(node.properties)}</p>` : ''}
        </div>
      `).join('')}
    </div>
  `

  htmlContent += `
    <h2>🔍 详细分析</h2>
    ${Object.entries(analysisResult.value.modelResults).map(([, modelResult]) => {
      if (modelResult) {
        return `
          <div class="model-result">
            <h3>${modelResult.name}</h3>
            <p>${modelResult.description}</p>
            ${modelResult.keyFindings.length > 0 ? `
              <h4>关键发现:</h4>
              <ul>
                ${modelResult.keyFindings.map(finding => `<li>${finding}</li>`).join('')}
              </ul>
            ` : ''}
            ${modelResult.recommendations.length > 0 ? `
              <h4>建议:</h4>
              <ul>
                ${modelResult.recommendations.map(rec => `<li>${rec}</li>`).join('')}
              </ul>
            ` : ''}
          </div>
        `
      }
      return ''
    }).join('')}
  `

  htmlContent += `</body></html>`

  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(htmlContent)
    printWindow.document.close()
    printWindow.focus()
    setTimeout(() => {
      printWindow.print()
      printWindow.close()
    }, 500)
  }
}

const retryAnalysis = () => {
  analysisError.value = ''
  startAnalysis()
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
    <div class="h-screen flex flex-col">
      <!-- 优化的头部 -->
      <div class="flex items-center justify-between p-4 sm:p-6 border-b border-slate-200 dark:border-slate-700 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-sm">
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-xl sm:text-2xl font-bold text-slate-800 dark:text-slate-200">{{ translations.pageTitle }}</h1>
              <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400 mt-1">
                <span class="font-medium">{{ translations.topicPrefix }}</span>{{ topic }}
              </p>
            </div>
          </div>
        </div>

        <!-- 状态和操作区域 -->
        <div class="flex items-center space-x-3">
          <!-- 分析状态指示器 -->
          <div v-if="isLoading" class="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
            <div class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span class="text-sm font-medium hidden sm:inline">{{ loadingMessage }}</span>
          </div>
          <div v-else-if="analysisError" class="flex items-center space-x-2 text-red-600 dark:text-red-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium hidden sm:inline">分析失败</span>
          </div>
          <div v-else-if="analysisResult" class="flex items-center space-x-2 text-green-600 dark:text-green-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-sm font-medium hidden sm:inline">{{ analysisResult.summary.totalNodes }} 个节点</span>
          </div>

          <!-- 操作按钮 -->
          <button
            v-if="analysisResult"
            @click="exportAnalysis"
            class="flex items-center space-x-2 px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors text-sm font-medium"
            :title="translations.exportAnalysis"
          >
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <span class="hidden sm:inline">{{ translations.export }}</span>
          </button>
          <button
            @click="$emit('close')"
            class="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-500 dark:text-slate-400 hover:text-slate-700 dark:hover:text-slate-300 transition-colors"
          >
            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- 优化的控制面板 -->
      <div
        :class="[
          'border-b border-slate-200 dark:border-slate-700 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm transition-all duration-500 ease-in-out',
          isControlPanelCollapsed ? 'max-h-20 overflow-hidden' : 'max-h-screen'
        ]"
      >
        <div class="p-4 sm:p-6">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">分析配置</h3>
            </div>
            <button
              v-if="analysisResult"
              @click="isControlPanelCollapsed = !isControlPanelCollapsed"
              class="p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-400 dark:text-slate-500 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
              :title="isControlPanelCollapsed ? '展开配置' : '收起配置'"
            >
              <svg v-if="isControlPanelCollapsed" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
              <svg v-else class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
              </svg>
            </button>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 思考深度控制 -->
            <div class="lg:col-span-1">
              <div class="bg-white dark:bg-slate-800 rounded-xl p-4 border border-slate-200 dark:border-slate-700 shadow-sm">
                <label class="block text-sm font-semibold text-slate-700 dark:text-slate-300 mb-3">
                  {{ translations.thinkingDepth }}
                </label>
                <div class="text-center mb-3">
                  <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {{ translations.depthLabels[thinkingDepth as 1 | 2 | 3 | 4 | 5] }}
                  </div>
                  <div class="text-xs text-slate-500 dark:text-slate-400">当前深度级别</div>
                </div>
                <div class="relative">
                  <input
                    v-model="thinkingDepth"
                    type="range"
                    min="1"
                    max="5"
                    step="1"
                    class="w-full h-3 bg-gradient-to-r from-blue-200 to-blue-500 rounded-lg appearance-none cursor-pointer slider"
                    :disabled="isLoading"
                  />
                  <div class="flex justify-between text-xs text-slate-500 dark:text-slate-400 mt-2">
                    <span>浅层</span>
                    <span>标准</span>
                    <span>深度</span>
                    <span>全面</span>
                    <span>极深</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 思维模型选择 -->
            <div class="lg:col-span-2">
              <div class="bg-white dark:bg-slate-800 rounded-xl p-4 border border-slate-200 dark:border-slate-700 shadow-sm">
                <ThinkingModelSelector v-model="selectedModels" :disabled="isLoading" />
              </div>
            </div>
          </div>
        </div>

        <!-- 分析按钮区域 -->
        <div class="mt-6 p-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-700 dark:to-slate-600 rounded-xl">
          <div class="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
            <button
              @click="startAnalysis"
              :disabled="!canStartAnalysis"
              class="w-full sm:w-auto flex items-center justify-center space-x-3 px-8 py-4 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-slate-400 disabled:to-slate-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div v-if="isLoading" class="flex items-center space-x-2">
                <div class="w-5 h-5 border-2 border-white/30 rounded-full animate-spin border-t-white"></div>
                <span>{{ loadingMessage }} ({{ currentLoadingStep }}/{{ totalLoadingSteps }})</span>
              </div>
              <div v-else class="flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <span>{{ analysisResult ? '重新分析' : '开始AI系统化分析' }}</span>
              </div>
            </button>

            <!-- 分析进度指示 -->
            <div v-if="isLoading" class="flex items-center space-x-2 text-sm text-slate-600 dark:text-slate-400">
              <div class="flex space-x-1">
                <div v-for="i in totalLoadingSteps" :key="i" :class="[
                  'w-2 h-2 rounded-full transition-all duration-300',
                  i <= currentLoadingStep ? 'bg-blue-500' : 'bg-slate-300 dark:bg-slate-600'
                ]"></div>
              </div>
              <span class="hidden sm:inline">步骤 {{ currentLoadingStep }}/{{ totalLoadingSteps }}</span>
            </div>
          </div>
        </div>

        <!-- 错误提示 -->
        <div v-if="analysisError" class="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl">
          <div class="flex items-start justify-between">
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="h-5 w-5 text-red-600 dark:text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 class="text-sm font-semibold text-red-800 dark:text-red-300">分析失败</h3>
                <p class="mt-1 text-sm text-red-700 dark:text-red-400">{{ analysisError }}</p>
              </div>
            </div>
            <button
              @click="retryAnalysis"
              class="flex items-center space-x-1 px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 font-medium bg-red-100 dark:bg-red-900/30 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              <span>重试</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 分析结果 -->
      <div class="flex-1 overflow-hidden relative">
        <div v-if="analysisResult" class="h-full flex flex-col lg:flex-row">
          <!-- 网络图 -->
          <div class="flex-1 p-2 sm:p-4 min-h-0 relative">
            <NetworkGraph
              :nodes="analysisResult.nodes"
              :connections="analysisResult.connections"
              :thinking-depth="thinkingDepth"
            />

            <!-- 移动端侧边栏切换按钮 -->
            <button
              @click="isSidebarCollapsed = !isSidebarCollapsed"
              class="lg:hidden fixed bottom-4 right-4 z-10 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all duration-200"
            >
              <svg v-if="isSidebarCollapsed" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <svg v-else class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- 优化的侧边栏详情 -->
          <div
            :class="[
              'w-full lg:w-80 border-t lg:border-t-0 lg:border-l border-slate-200 dark:border-slate-700 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm overflow-y-auto transition-all duration-300',
              'lg:max-h-none lg:relative lg:translate-x-0',
              isSidebarCollapsed
                ? 'max-h-0 lg:max-h-none fixed lg:relative inset-x-0 bottom-0 z-20 translate-y-full lg:translate-y-0'
                : 'max-h-96 lg:max-h-none fixed lg:relative inset-x-0 bottom-0 z-20 translate-y-0'
            ]"
          >
            <div class="p-4">
              <!-- 侧边栏头部 -->
              <div class="flex items-center justify-between mb-4 pb-3 border-b border-slate-200 dark:border-slate-700">
                <div class="flex items-center space-x-2">
                  <div class="w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold text-slate-800 dark:text-slate-200">分析详情</h3>
                </div>
                <button
                  @click="isSidebarCollapsed = !isSidebarCollapsed"
                  class="lg:hidden p-1 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 text-slate-500 dark:text-slate-400"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>

              <!-- 统计信息卡片 -->
              <div class="grid grid-cols-2 gap-3 mb-6">
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 p-3 rounded-xl border border-blue-200 dark:border-blue-700">
                  <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ analysisResult.summary.totalNodes }}</div>
                  <div class="text-xs font-medium text-blue-700 dark:text-blue-300">分析节点</div>
                </div>
                <div class="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/30 dark:to-green-800/30 p-3 rounded-xl border border-green-200 dark:border-green-700">
                  <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ analysisResult.summary.totalConnections }}</div>
                  <div class="text-xs font-medium text-green-700 dark:text-green-300">关联连接</div>
                </div>
                <div class="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/30 dark:to-purple-800/30 p-3 rounded-xl border border-purple-200 dark:border-purple-700">
                  <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ analysisResult.summary.totalTokenCount || 0 }}</div>
                  <div class="text-xs font-medium text-purple-700 dark:text-purple-300">总Token</div>
                </div>
                <div class="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/30 dark:to-orange-800/30 p-3 rounded-xl border border-orange-200 dark:border-orange-700">
                  <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ (analysisResult.summary.averageTokensPerSecond || 0).toFixed(0) }}</div>
                  <div class="text-xs font-medium text-orange-700 dark:text-orange-300">Token/秒</div>
                </div>
              </div>

              <!-- 关键洞察 -->
              <div class="mb-6">
                <div class="flex items-center space-x-2 mb-3">
                  <div class="w-5 h-5 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                  </div>
                  <h4 class="font-semibold text-slate-800 dark:text-slate-200">关键洞察</h4>
                </div>
                <div class="space-y-2">
                  <div
                    v-for="(insight, index) in analysisResult.summary.keyInsights"
                    :key="insight"
                    class="flex items-start space-x-3 p-3 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-lg border border-amber-200 dark:border-amber-700"
                  >
                    <div class="w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                      {{ index + 1 }}
                    </div>
                    <p class="text-sm text-slate-700 dark:text-slate-300 leading-relaxed">{{ insight }}</p>
                  </div>
                </div>
              </div>

              <!-- 思维模型结果 -->
              <div class="space-y-4">
                <div class="flex items-center space-x-2 mb-3">
                  <div class="w-5 h-5 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-lg flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                  </div>
                  <h4 class="font-semibold text-slate-800 dark:text-slate-200">思维模型分析</h4>
                </div>

                <div v-for="(modelResult, modelId) in analysisResult.modelResults" :key="modelId" class="bg-white dark:bg-slate-800 rounded-xl p-4 border border-slate-200 dark:border-slate-700 shadow-sm">
                  <div class="flex items-center space-x-2 mb-3">
                    <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                    </div>
                    <h5 class="font-semibold text-slate-800 dark:text-slate-200">{{ modelResult?.name }}</h5>
                  </div>
                  <p class="text-sm text-slate-600 dark:text-slate-400 mb-4 leading-relaxed">
                    {{ modelResult?.description }}
                  </p>

                  <!-- 关键发现 -->
                  <div v-if="modelResult?.keyFindings?.length" class="mb-4">
                    <h6 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 flex items-center space-x-1">
                      <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      <span>关键发现</span>
                    </h6>
                    <div class="space-y-2">
                      <div v-for="finding in modelResult.keyFindings" :key="finding" class="text-sm text-slate-600 dark:text-slate-400 bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-l-4 border-blue-400">
                        {{ finding }}
                      </div>
                    </div>
                  </div>

                  <!-- 建议 -->
                  <div v-if="modelResult?.recommendations?.length">
                    <h6 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 flex items-center space-x-1">
                      <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                      </svg>
                      <span>建议</span>
                    </h6>
                    <div class="space-y-2">
                      <div v-for="rec in modelResult.recommendations" :key="rec" class="text-sm text-slate-600 dark:text-slate-400 bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border-l-4 border-green-400">
                        {{ rec }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 配置状态 - 等待用户配置 -->
        <div v-else-if="!isLoading" class="h-full flex items-center justify-center p-4">
          <div class="text-center max-w-md">
            <svg class="mx-auto h-12 w-12 sm:h-16 sm:w-16 text-slate-400 dark:text-slate-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
            </svg>
            <h3 class="text-base sm:text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">配置分析参数</h3>
            <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400 mb-4">
              请选择思维模型和分析深度，然后点击"开始AI系统化分析"按钮
            </p>
            <div class="bg-blue-50/80 dark:bg-blue-900/20 border border-blue-200/50 dark:border-blue-800/50 rounded-lg p-3 sm:p-4 text-left">
              <h4 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">💡 分析提示</h4>
              <ul class="text-xs sm:text-sm text-blue-700 dark:text-blue-400 space-y-1">
                <li>• 选择多个思维模型可获得更全面的分析</li>
                <li>• 思考深度越高，分析越详细但耗时更长</li>
                <li>• 建议首次使用选择2-3个模型，标准深度</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-else class="h-full flex items-center justify-center p-4">
          <div class="text-center">
            <div class="inline-flex items-center justify-center w-12 h-12 sm:w-16 sm:h-16 bg-blue-100/80 dark:bg-blue-900/30 rounded-full mb-4 glass-effect">
              <div class="w-6 h-6 sm:w-8 sm:h-8 border-3 border-blue-200 dark:border-blue-700 rounded-full animate-spin border-t-blue-600 dark:border-t-blue-400"></div>
            </div>
            <h3 class="text-base sm:text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">{{ loadingMessage }}</h3>
            <p class="text-sm sm:text-base text-slate-600 dark:text-slate-400 mb-4">运用多种思维模型和人工智能深度分析中...</p>
            <div class="w-48 sm:w-64 bg-slate-200/50 dark:bg-slate-700/50 rounded-full h-2 mx-auto">
              <div 
                class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${totalLoadingSteps > 0 ? (currentLoadingStep / totalLoadingSteps) * 100 : 0}%` }"
              ></div>
            </div>
            <p class="text-sm text-slate-500 dark:text-slate-400 mt-2">{{ currentLoadingStep }}/{{ totalLoadingSteps }} 模型</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #2563eb;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #2563eb;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.dark .slider::-webkit-slider-thumb {
  background: #3b82f6;
}

.dark .slider::-moz-range-thumb {
  background: #3b82f6;
}

.glass-morphism {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark .glass-morphism {
  background-color: rgba(30, 41, 59, 0.95);
  border: 1px solid rgba(71, 85, 105, 0.3);
}

.glass-effect {
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background-color: rgba(30, 41, 59, 0.4);
  border: 1px solid rgba(71, 85, 105, 0.2);
}
</style>